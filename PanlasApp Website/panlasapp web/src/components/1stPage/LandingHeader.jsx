import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import './LandingHeader.css'
import QRCode from 'qrcode';

//images
import logo from "../../assets/PanlasApp.png";

const LandingHeader = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [showMobileAppModal, setShowMobileAppModal] = useState(false);
  const canvasRef = useRef(null);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const openMobileAppModal = () => {
    setShowMobileAppModal(true);
  };

  const closeMobileAppModal = () => {
    setShowMobileAppModal(false);
  };
  
  
  // Generate QR code when modal opens
  useEffect(() => {
    const generateQR = async () => {
      if (canvasRef.current && showMobileAppModal) {
        try {
          // Using PanlasApp Play Store URL (placeholder for now)
          const playStoreUrl = 'https://drive.google.com/file/d/1eg7wCgdwnjFWvFdFQ5pQSRfESPfbieA1/view?usp=drive_link';

          await QRCode.toCanvas(canvasRef.current, playStoreUrl, {
            width: 150,
            margin: 2,
            color: {
              dark: '#20C5AF',
              light: '#FFFFFF'
            }
          });
        } catch (error) {
          console.error('Error generating QR code:', error);
        }
      }
    };

    if (showMobileAppModal) {
      generateQR();
    }
  }, [showMobileAppModal]);

  return (
    <header className="landing-header">
      <div className="container">
        <div className="header-content">
          <Link to="/" className="logo-container">
            <div className="logo">
              <img src={logo} alt="PanlasApp Logo" />
            </div>
            <div className="logo-text-group">
              <span className="logo-text">PanlasApp</span>
              <span className="meal-planning-tag">for meal planning</span>
            </div>
          </Link>
          <button className="mobile-menu-btn" onClick={toggleMenu} aria-label="Toggle menu">
            <span className={`hamburger ${menuOpen ? 'active' : ''}`}></span>
          </button>
          <nav className={`main-nav ${menuOpen ? 'active' : ''}`}>
            <ul>
              <li><Link to="/login" className="btn-login">Log In</Link></li>
              <li><Link to="/signup" className="btn-signup">Sign Up</Link></li>
              <li>
                <button
                  onClick={openMobileAppModal}
                  className="btn-mobile-app"
                >
                  📱 Visit our mobile app
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Mobile App QR Code Modal - Simple Single Card */}
      {showMobileAppModal && (
        <div className="mobile-app-modal-overlay" onClick={closeMobileAppModal}>
          <div className="mobile-app-card" onClick={(e) => e.stopPropagation()}>
            <button className="modal-close-btn" onClick={closeMobileAppModal}>
              ×
            </button>

            <h3>📱 Download PanlasApp</h3>
            <p>Scan QR code or click button below</p>

            <div className="qr-code-section">
              <div className="qr-code-wrapper">
                <canvas
                  ref={canvasRef}
                  className="qr-canvas"
                />
              </div>
            </div>

            <a
              href="https://drive.google.com/file/d/1eg7wCgdwnjFWvFdFQ5pQSRfESPfbieA1/view?usp=drive_link"
              target="_blank"
              rel="noopener noreferrer"
              className="play-store-btn"
            >
              📱 Download App
            </a>
          </div>
        </div>
      )}
    </header>
  );
};

export default LandingHeader;